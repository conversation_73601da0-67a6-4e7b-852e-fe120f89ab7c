<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Server Save</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .test-data { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; font-family: monospace; font-size: 12px; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; font-family: monospace; font-size: 11px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Server Save Test</h1>
    <p>This page tests if the server endpoints are working correctly for saving icon data.</p>

    <div class="test-section">
        <h2>Current Server Data</h2>
        <button onclick="loadCurrentData()">Load Current icons-data.json</button>
        <div id="currentDataResult"></div>
        <div id="currentDataDisplay" class="test-data"></div>
    </div>

    <div class="test-section">
        <h2>Test Python Server Endpoint</h2>
        <button onclick="testPythonEndpoint()">Test /api/save-icons</button>
        <div id="pythonResult"></div>
    </div>

    <div class="test-section">
        <h2>Test PHP Server Endpoint</h2>
        <button onclick="testPhpEndpoint()">Test save-icons.php</button>
        <div id="phpResult"></div>
    </div>

    <div class="test-section">
        <h2>Test Admin Panel Save Function</h2>
        <button onclick="testAdminSave()">Test Admin saveIconsData()</button>
        <div id="adminResult"></div>
    </div>

    <div class="test-section">
        <h2>Debug Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        const testData = [
            {
                "id": "test-save-icon",
                "name": "Test Save Icon",
                "category": "test",
                "svg": "<svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"><circle cx=\"12\" cy=\"12\" r=\"10\"/><path d=\"M12 6v6l4 2\"/></svg>"
            }
        ];

        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function loadCurrentData() {
            log('Loading current server data...');
            try {
                const response = await fetch('icons-data.json');
                log(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                log(`Loaded ${data.length} icons from server`);
                
                document.getElementById('currentDataDisplay').textContent = JSON.stringify(data, null, 2);
                showResult('currentDataResult', `✅ Loaded ${data.length} icons from server`, 'success');
            } catch (error) {
                log(`Error loading current data: ${error.message}`);
                showResult('currentDataResult', `❌ Failed to load: ${error.message}`, 'error');
            }
        }

        async function testPythonEndpoint() {
            log('Testing Python server endpoint /api/save-icons...');
            try {
                const response = await fetch('/api/save-icons', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                log(`Python endpoint response status: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log(`Python endpoint success: ${JSON.stringify(result)}`);
                    showResult('pythonResult', `✅ Python endpoint working! Saved ${testData.length} icons`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`Python endpoint error: ${errorText}`);
                    showResult('pythonResult', `❌ Python endpoint failed: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                log(`Python endpoint exception: ${error.message}`);
                showResult('pythonResult', `❌ Python endpoint error: ${error.message}`, 'error');
            }
        }

        async function testPhpEndpoint() {
            log('Testing PHP server endpoint save-icons.php...');
            try {
                const response = await fetch('save-icons.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                log(`PHP endpoint response status: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log(`PHP endpoint success: ${JSON.stringify(result)}`);
                    showResult('phpResult', `✅ PHP endpoint working! Saved ${testData.length} icons`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`PHP endpoint error: ${errorText}`);
                    showResult('phpResult', `❌ PHP endpoint failed: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                log(`PHP endpoint exception: ${error.message}`);
                showResult('phpResult', `❌ PHP endpoint error: ${error.message}`, 'error');
            }
        }

        async function testAdminSave() {
            log('Testing admin panel save function...');
            try {
                // Simulate the admin panel save function
                let serverSaveSuccess = false;
                let serverResponse = null;

                // Try Python server endpoint first
                try {
                    log('Admin test: Trying Python server endpoint...');
                    const response = await fetch('/api/save-icons', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(testData)
                    });

                    log(`Admin test Python response: ${response.status}`);
                    
                    if (response.ok) {
                        serverResponse = await response.json();
                        serverSaveSuccess = true;
                        log(`Admin test Python success: ${JSON.stringify(serverResponse)}`);
                    }
                } catch (pythonError) {
                    log(`Admin test Python error: ${pythonError.message}`);
                }

                // If Python failed, try PHP
                if (!serverSaveSuccess) {
                    try {
                        log('Admin test: Trying PHP server endpoint...');
                        const response = await fetch('save-icons.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify(testData)
                        });

                        log(`Admin test PHP response: ${response.status}`);
                        
                        if (response.ok) {
                            serverResponse = await response.json();
                            serverSaveSuccess = true;
                            log(`Admin test PHP success: ${JSON.stringify(serverResponse)}`);
                        }
                    } catch (phpError) {
                        log(`Admin test PHP error: ${phpError.message}`);
                    }
                }

                if (serverSaveSuccess) {
                    showResult('adminResult', `✅ Admin save function working! Data saved to server`, 'success');
                } else {
                    showResult('adminResult', `❌ Admin save function failed! No server endpoint working`, 'error');
                }

            } catch (error) {
                log(`Admin test exception: ${error.message}`);
                showResult('adminResult', `❌ Admin test error: ${error.message}`, 'error');
            }
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        // Auto-load current data on page load
        window.onload = function() {
            log('Page loaded, starting tests...');
            loadCurrentData();
        };
    </script>
</body>
</html>
