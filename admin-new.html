<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG Icons Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        /* Login Form Styles */
        .login-container {
            max-width: 400px;
            margin: 100px auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            text-align: center;
        }

        .login-container h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }

        /* Admin Panel Styles */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: none;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            font-size: 32px;
        }

        .back-button {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .logout-button {
            position: absolute;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .logout-button:hover {
            background: #c82333;
        }

        .content {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .icons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .icon-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .icon-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .icon-preview {
            width: 64px;
            height: 64px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .icon-preview svg {
            width: 48px;
            height: 48px;
        }

        .icon-info h3 {
            color: #333;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .icon-info p {
            color: #6c757d;
            margin: 0 0 5px 0;
            font-size: 12px;
        }

        .icon-actions {
            margin-top: 15px;
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .edit-btn,
        .delete-btn {
            padding: 8px 16px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: bold;
            color: white;
        }

        .edit-btn {
            background: #28a745;
        }

        .edit-btn:hover {
            background: #218838;
        }

        .delete-btn {
            background: #dc3545;
        }

        .delete-btn:hover {
            background: #c82333;
        }

        .message {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #eee;
        }

        .section:last-child {
            border-bottom: none;
        }

        .section h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 24px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .search-controls {
            display: grid;
            grid-template-columns: 1fr 200px 200px;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
        }

        .search-controls .form-group {
            margin-bottom: 0;
        }

        /* File Upload Styles */
        .file-upload-area {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-area:hover,
        .file-upload-area.dragover {
            border-color: #0056b3;
            background: #e6f3ff;
            transform: translateY(-2px);
        }

        .file-upload-content {
            pointer-events: none;
        }

        .file-upload-area.processing {
            border-color: #28a745;
            background: #f0f8f0;
        }

        .upload-status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }

        .upload-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .upload-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .upload-status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Navigation Tabs */
        .admin-nav {
            display: flex;
            gap: 0;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-btn {
            background: none;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            color: #007bff;
            background: #f8f9fa;
        }

        .nav-btn.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: #f8f9fa;
        }

        /* Admin Sections */
        .admin-section {
            display: none;
        }

        .admin-section.active {
            display: block;
        }

        /* Enhanced Stats */
        .stats-header {
            margin-bottom: 30px;
        }

        /* Categories Management */
        .categories-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .category-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-info {
            flex: 1;
        }

        .category-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .category-count {
            font-size: 12px;
            color: #6c757d;
        }

        .category-actions {
            display: flex;
            gap: 8px;
        }

        .category-actions button {
            padding: 6px 12px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .edit-category-btn {
            background: #28a745;
            color: white;
        }

        .edit-category-btn:hover {
            background: #218838;
        }

        .delete-category-btn {
            background: #dc3545;
            color: white;
        }

        .delete-category-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <!-- Login Form (shown initially) -->
    <div id="loginForm" class="login-container">
        <h1>Admin Login</h1>
        <form id="loginFormElement">
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter admin password" required>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">Login</button>
        </form>
        <div id="loginError" class="message error" style="margin-top: 15px;"></div>
    </div>

    <!-- Admin Panel (hidden initially) -->
    <div id="adminPanel" class="container">
        <div class="header">
            <a href="index.html" class="back-button">🏠 Back to Main App</a>
            <h1>SVG Icons Admin Panel</h1>
            <div class="header-controls">
                <button id="logoutBtn" class="logout-button">Logout</button>
                <button id="testBtn" class="btn btn-secondary" style="margin-left: 10px;">Test Buttons</button>
            </div>
        </div>

        <div class="content">
            <div id="message" class="message"></div>

            <!-- Enhanced Statistics -->
            <div class="stats-header">
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalIcons">0</div>
                        <div class="stat-label">Total Icons</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalCategories">0</div>
                        <div class="stat-label">Categories</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Top Categories:</div>
                        <div id="topCategories" style="font-size: 12px; margin-top: 5px;">Loading...</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Last updated:</div>
                        <div id="lastUpdated" style="font-size: 12px; margin-top: 5px;">Never</div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <nav class="admin-nav">
                <button class="nav-btn active" data-section="icons">Manage Icons</button>
                <button class="nav-btn" data-section="categories">Manage Categories</button>
                <button class="nav-btn" data-section="importExport">Import/Export</button>
            </nav>

            <!-- Manage Icons Section -->
            <div id="iconsSection" class="admin-section active">
                <!-- Add New Icon -->
                <div class="section">
                    <h2>Add New Icon</h2>

                <!-- SVG File Upload Section -->
                <div style="margin-bottom: 30px;">
                    <h3 style="color: #495057; font-size: 18px; margin-bottom: 15px;">Import SVG File</h3>
                    <div class="file-upload-area" id="fileUploadArea">
                        <div class="file-upload-content">
                            <svg style="width: 48px; height: 48px; color: #6c757d; margin-bottom: 15px;" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                <polyline points="14,2 14,8 20,8"/>
                                <line x1="16" y1="13" x2="8" y2="13"/>
                                <line x1="16" y1="17" x2="8" y2="17"/>
                                <polyline points="10,9 9,9 8,9"/>
                            </svg>
                            <p style="margin: 0 0 10px 0; color: #495057; font-weight: 500;">Drag & drop SVG files here</p>
                            <p style="margin: 0 0 15px 0; color: #6c757d; font-size: 14px;">or</p>
                            <button type="button" class="btn btn-secondary" id="selectFileBtn">Select SVG File</button>
                            <input type="file" id="svgFileInput" accept=".svg" style="display: none;" multiple>
                        </div>
                    </div>
                    <div id="fileUploadStatus" style="margin-top: 15px; display: none;"></div>
                </div>

                <!-- Manual Entry Form -->
                <div style="margin-bottom: 20px;">
                    <h3 style="color: #495057; font-size: 18px; margin-bottom: 15px;">Or Enter Manually</h3>
                </div>

                <form id="addIconForm">
                    <div class="form-group">
                        <label for="iconId">Icon ID:</label>
                        <input type="text" id="iconId" placeholder="e.g., my-new-icon" required>
                    </div>
                    <div class="form-group">
                        <label for="iconName">Icon Name:</label>
                        <input type="text" id="iconName" placeholder="e.g., My New Icon" required>
                    </div>
                    <div class="form-group">
                        <label for="iconCategory">Category:</label>
                        <select id="iconCategory" required>
                            <option value="">Select Category</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="iconSvg">SVG Code:</label>
                        <textarea id="iconSvg" placeholder="Paste your SVG code here..." required></textarea>
                        <div id="svgPreview" style="margin-top: 15px; padding: 20px; border: 2px dashed #e9ecef; border-radius: 8px; text-align: center; display: none; background: #f8f9fa;">
                            <p style="margin: 0 0 15px 0; color: #6c757d; font-size: 14px;">SVG Preview:</p>
                            <div id="svgPreviewContent" style="display: flex; align-items: center; justify-content: center; width: 64px; height: 64px; margin: 0 auto; background: white; border: 1px solid #dee2e6; border-radius: 8px;">
                                <!-- Preview will appear here -->
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Add Icon</button>
                    <button type="button" id="previewBtn" class="btn btn-secondary">Preview SVG</button>
                    <button type="button" id="clearFormBtn" class="btn btn-secondary">Clear Form</button>
                </form>
                </div>

                <!-- Existing Icons -->
                <div class="section">
                    <h2>Existing Icons</h2>

                    <!-- Search and Filter Controls -->
                    <div class="search-controls">
                        <div class="form-group">
                            <label for="iconSearch">Search Icons:</label>
                            <input type="text" id="iconSearch" placeholder="Search by name or ID...">
                        </div>
                        <div class="form-group">
                            <label for="categoryFilter">Filter by Category:</label>
                            <select id="categoryFilter">
                                <option value="">All Categories</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="sortOrder">Sort by:</label>
                            <select id="sortOrder">
                                <option value="name-asc">Name (A-Z)</option>
                                <option value="name-desc">Name (Z-A)</option>
                                <option value="category-asc">Category (A-Z)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Icons Count Display -->
                    <div id="iconsCount" style="margin-bottom: 15px; color: #6c757d; font-size: 14px;">
                        Loading icons...
                    </div>

                    <div id="iconsGrid" class="icons-grid">
                        <!-- Icons will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Manage Categories Section -->
            <div id="categoriesSection" class="admin-section">
                <div class="section">
                    <h2>Manage Categories</h2>

                    <!-- Add New Category -->
                    <div style="margin-bottom: 30px;">
                        <h3>Add New Category</h3>
                        <form id="addCategoryForm" style="display: flex; gap: 15px; align-items: end;">
                            <div class="form-group" style="flex: 1; margin-bottom: 0;">
                                <label for="newCategoryName">Category Name:</label>
                                <input type="text" id="newCategoryName" placeholder="e.g., social-media" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Add Category</button>
                        </form>
                    </div>

                    <!-- Existing Categories -->
                    <div>
                        <h3>Existing Categories</h3>
                        <div id="categoriesList" class="categories-list">
                            <!-- Categories will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Import/Export Section -->
            <div id="importExportSection" class="admin-section">
                <div class="section">
                    <h2>Import/Export Data</h2>

                    <!-- Export Section -->
                    <div style="margin-bottom: 40px;">
                        <h3>Export Icon Library</h3>
                        <p style="color: #6c757d; margin-bottom: 20px;">Download the complete icon library data in different formats.</p>
                        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                            <button id="exportJsonBtn" class="btn btn-success">Export JSON</button>
                            <button id="exportCsvBtn" class="btn btn-success">Export CSV</button>
                            <button id="createBackupBtn" class="btn btn-success">Create Backup</button>
                        </div>
                    </div>

                    <!-- Import Section -->
                    <div>
                        <h3>Import Icon Library</h3>
                        <p style="color: #6c757d; margin-bottom: 20px;">Upload a JSON file to replace or merge with the current icon library.</p>

                        <div class="form-group">
                            <label for="jsonFileInput">Select JSON File:</label>
                            <input type="file" id="jsonFileInput" accept=".json" style="margin-bottom: 15px;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: flex; align-items: center; margin-bottom: 10px;">
                                <input type="radio" name="importMode" value="replace" checked style="margin-right: 8px;">
                                Replace existing data
                            </label>
                            <label style="display: flex; align-items: center;">
                                <input type="radio" name="importMode" value="merge" style="margin-right: 8px;">
                                Merge with existing data
                            </label>
                        </div>

                        <button id="importJsonBtn" class="btn btn-primary">Import JSON</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer style="margin-top: 50px; padding: 20px 0; border-top: 1px solid #eee; text-align: center; color: #6c757d; font-size: 14px;">
            <p style="margin: 0;">
                SVG Icons Library Admin Panel |
                <a href="https://github.com" target="_blank" style="color: #007bff; text-decoration: none;">GitHub</a> |
                Built with ❤️ for developers
            </p>
        </footer>
    </div>

    <script src="admin-new.js"></script>
</body>
</html>
