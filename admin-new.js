class AdminPanel {
    constructor() {
        this.iconsData = [];
        this.filteredIcons = [];
        this.categories = new Set();
        this.editingIconId = null;
        this.adminPassword = '@admin1234';
        this.isLoggedIn = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadIconsData();
    }

    setupEventListeners() {
        // Login form
        document.getElementById('loginFormElement').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.handleLogout();
        });

        // Test button
        document.getElementById('testBtn').addEventListener('click', () => {
            this.testButtons();
        });

        // Add icon form
        document.getElementById('addIconForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addIcon();
        });

        // Preview button
        document.getElementById('previewBtn').addEventListener('click', () => {
            this.previewSVG();
        });

        // Auto-preview when SVG code changes (with debounce)
        let previewTimeout;
        document.getElementById('iconSvg').addEventListener('input', () => {
            clearTimeout(previewTimeout);
            previewTimeout = setTimeout(() => {
                this.previewSVG();
            }, 500); // Wait 500ms after user stops typing
        });

        // Clear form button
        document.getElementById('clearFormBtn').addEventListener('click', () => {
            this.clearForm();
        });

        // File upload functionality
        this.setupFileUpload();

        // Navigation tabs
        this.setupNavigation();

        // Category management
        this.setupCategoryManagement();

        // Import/Export functionality
        this.setupImportExport();

        // Search and filter controls
        document.getElementById('iconSearch').addEventListener('input', () => {
            this.filterIcons();
        });

        document.getElementById('categoryFilter').addEventListener('change', () => {
            this.filterIcons();
        });

        document.getElementById('sortOrder').addEventListener('change', () => {
            this.filterIcons();
        });
    }

    handleLogin() {
        const password = document.getElementById('password').value;
        const loginError = document.getElementById('loginError');

        if (password === this.adminPassword) {
            this.isLoggedIn = true;
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('adminPanel').style.display = 'block';
            this.updateCategoriesDropdown();
            this.renderIcons();
            this.updateStats();
            this.updateLastModified();
        } else {
            loginError.textContent = 'Invalid password. Please try again.';
            loginError.style.display = 'block';
            setTimeout(() => {
                loginError.style.display = 'none';
            }, 3000);
        }
    }

    handleLogout() {
        this.isLoggedIn = false;
        document.getElementById('loginForm').style.display = 'block';
        document.getElementById('adminPanel').style.display = 'none';
        document.getElementById('password').value = '';
        document.getElementById('addIconForm').reset();
        this.editingIconId = null;
    }

    async loadIconsData() {
        try {
            // Try to load from icons-data.json file first
            try {
                const response = await fetch('icons-data.json');
                if (response.ok) {
                    this.iconsData = await response.json();
                    console.log('Loaded icons from icons-data.json:', this.iconsData.length);
                } else {
                    throw new Error('Failed to fetch icons-data.json');
                }
            } catch (fetchError) {
                console.log('Could not load from icons-data.json, trying localStorage...');
                // Fallback to localStorage
                const data = localStorage.getItem('iconsData');
                if (data) {
                    this.iconsData = JSON.parse(data);
                    console.log('Loaded icons from localStorage:', this.iconsData.length);
                } else {
                    // If no data in localStorage, initialize with empty array
                    this.iconsData = [];
                    console.log('No existing data found, starting with empty array');
                }
            }

            // Extract categories
            this.categories.clear();
            this.iconsData.forEach(icon => {
                this.categories.add(icon.category);
            });

            this.filteredIcons = [...this.iconsData];

            if (this.isLoggedIn) {
                this.updateCategoriesDropdown();
                this.renderIcons();
                this.updateStats();
                this.updateLastModified();
            }
        } catch (error) {
            console.error('Error loading icons data:', error);
            if (this.isLoggedIn) {
                this.showMessage('Error loading icons data', 'error');
            }
        }
    }

    async saveIconsData() {
        try {
            // Save to localStorage
            localStorage.setItem('iconsData', JSON.stringify(this.iconsData));

            // Try to save to server (icons-data.json file)
            try {
                // Try Python server endpoint first
                let response = await fetch('/api/save-icons', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(this.iconsData)
                });

                if (!response.ok) {
                    // Fallback to PHP endpoint
                    response = await fetch('save-icons.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(this.iconsData)
                    });
                }

                if (response.ok) {
                    const result = await response.json();
                    console.log('Icons data saved to server successfully:', result);
                    this.updateLastModified();
                    this.showMessage(`Successfully saved ${this.iconsData.length} icons to server!`, 'success');
                } else {
                    console.log('Server save failed, data saved to localStorage only');
                    this.showMessage('Server save failed, data saved to localStorage only', 'warning');
                }
            } catch (serverError) {
                console.log('Server not available, data saved to localStorage only:', serverError);
                this.showMessage('Server not available, data saved to localStorage only', 'warning');
            }

        } catch (error) {
            console.error('Error saving icons data:', error);
            this.showMessage('Error saving icons data', 'error');
        }
    }

    updateCategoriesDropdown() {
        // Update main category dropdown
        const select = document.getElementById('iconCategory');
        select.innerHTML = '<option value="">Select Category</option>';

        Array.from(this.categories).sort().forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
            select.appendChild(option);
        });

        // Update filter dropdown
        const filterSelect = document.getElementById('categoryFilter');
        filterSelect.innerHTML = '<option value="">All Categories</option>';

        Array.from(this.categories).sort().forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
            filterSelect.appendChild(option);
        });
    }

    filterIcons() {
        const searchTerm = document.getElementById('iconSearch').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const sortOrder = document.getElementById('sortOrder').value;

        // Filter icons
        let filtered = this.iconsData.filter(icon => {
            const matchesSearch = icon.name.toLowerCase().includes(searchTerm) ||
                                icon.id.toLowerCase().includes(searchTerm);
            const matchesCategory = !categoryFilter || icon.category === categoryFilter;
            return matchesSearch && matchesCategory;
        });

        // Sort icons
        filtered.sort((a, b) => {
            switch (sortOrder) {
                case 'name-asc':
                    return a.name.localeCompare(b.name);
                case 'name-desc':
                    return b.name.localeCompare(a.name);
                case 'category-asc':
                    return a.category.localeCompare(b.category);
                default:
                    return a.name.localeCompare(b.name);
            }
        });

        this.filteredIcons = filtered;
        this.renderIcons();
    }

    addIcon() {
        const id = document.getElementById('iconId').value.trim();
        const name = document.getElementById('iconName').value.trim();
        const category = document.getElementById('iconCategory').value;
        const svg = document.getElementById('iconSvg').value.trim();

        if (!id || !name || !category || !svg) {
            this.showMessage('Please fill in all fields', 'error');
            return;
        }

        // Check if ID already exists (only for new icons)
        if (!this.editingIconId && this.iconsData.find(icon => icon.id === id)) {
            this.showMessage('Icon ID already exists. Please choose a different ID.', 'error');
            return;
        }

        // Validate SVG
        if (!svg.includes('<svg') || !svg.includes('</svg>')) {
            this.showMessage('Please enter valid SVG code', 'error');
            return;
        }

        // Clean SVG
        const cleanSvg = this.cleanSVG(svg);

        if (this.editingIconId) {
            // Update existing icon
            const iconIndex = this.iconsData.findIndex(icon => icon.id === this.editingIconId);
            if (iconIndex !== -1) {
                this.iconsData[iconIndex] = { id, name, category, svg: cleanSvg };
                this.showMessage('Icon updated successfully!', 'success');
            }
            this.editingIconId = null;
            document.querySelector('#addIconForm button[type="submit"]').textContent = 'Add Icon';
        } else {
            // Add new icon
            this.iconsData.push({ id, name, category, svg: cleanSvg });
            this.categories.add(category);
            this.showMessage('Icon added successfully!', 'success');
        }

        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.filterIcons(); // Use filterIcons instead of renderIcons to maintain current filter
        this.updateStats();
        document.getElementById('addIconForm').reset();
        this.hidePreview();
    }

    cleanSVG(svg) {
        // Remove any script tags and clean the SVG
        return svg.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                 .replace(/on\w+="[^"]*"/g, '')
                 .trim();
    }

    previewSVG() {
        const svgCode = document.getElementById('iconSvg').value.trim();
        const previewContainer = document.getElementById('svgPreview');
        const previewContent = document.getElementById('svgPreviewContent');

        if (!svgCode) {
            this.hidePreview();
            return;
        }

        // Validate SVG
        if (!svgCode.includes('<svg') || !svgCode.includes('</svg>')) {
            previewContent.innerHTML = '<p style="color: #dc3545; font-size: 12px; margin: 0;">Invalid SVG code</p>';
            previewContainer.style.display = 'block';
            return;
        }

        try {
            // Clean the SVG
            const cleanSvg = this.cleanSVG(svgCode);

            // Create a temporary div to validate the SVG
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = cleanSvg;

            // Check if it contains an SVG element
            const svgElement = tempDiv.querySelector('svg');
            if (!svgElement) {
                throw new Error('No SVG element found');
            }

            // Set the SVG size for preview
            svgElement.style.width = '48px';
            svgElement.style.height = '48px';

            // Display the preview
            previewContent.innerHTML = svgElement.outerHTML;
            previewContainer.style.display = 'block';

        } catch (error) {
            console.error('SVG Preview Error:', error);
            previewContent.innerHTML = '<p style="color: #dc3545; font-size: 12px; margin: 0;">Error rendering SVG</p>';
            previewContainer.style.display = 'block';
        }
    }

    hidePreview() {
        const previewContainer = document.getElementById('svgPreview');
        previewContainer.style.display = 'none';
    }

    clearForm() {
        document.getElementById('addIconForm').reset();
        this.hidePreview();
        this.editingIconId = null;
        document.querySelector('#addIconForm button[type="submit"]').textContent = 'Add Icon';
        this.hideUploadStatus();
    }

    setupFileUpload() {
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('svgFileInput');
        const selectFileBtn = document.getElementById('selectFileBtn');

        // Click to select files
        selectFileBtn.addEventListener('click', () => {
            fileInput.click();
        });

        fileUploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });

        // Drag and drop functionality
        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });

        fileUploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
        });

        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            this.handleFiles(e.dataTransfer.files);
        });
    }

    async handleFiles(files) {
        const fileArray = Array.from(files);
        const svgFiles = fileArray.filter(file => file.type === 'image/svg+xml' || file.name.toLowerCase().endsWith('.svg'));

        if (svgFiles.length === 0) {
            this.showUploadStatus('Please select valid SVG files only.', 'error');
            return;
        }

        if (svgFiles.length === 1) {
            // Single file - populate the form
            await this.processSingleFile(svgFiles[0]);
        } else {
            // Multiple files - batch import
            await this.processMultipleFiles(svgFiles);
        }
    }

    async processSingleFile(file) {
        try {
            this.showUploadStatus('Processing file...', 'info');

            const svgContent = await this.readFileAsText(file);
            const fileName = file.name.replace('.svg', '');

            // Auto-fill the form
            document.getElementById('iconId').value = this.generateIconId(fileName);
            document.getElementById('iconName').value = this.generateIconName(fileName);
            document.getElementById('iconSvg').value = svgContent;

            // Show preview
            this.previewSVG();

            this.showUploadStatus(`File "${file.name}" loaded successfully! Review and add the icon.`, 'success');

            // Scroll to form
            document.getElementById('addIconForm').scrollIntoView({ behavior: 'smooth' });

        } catch (error) {
            console.error('Error processing file:', error);
            this.showUploadStatus(`Error processing file "${file.name}": ${error.message}`, 'error');
        }
    }

    async processMultipleFiles(files) {
        try {
            this.showUploadStatus(`Processing ${files.length} files...`, 'info');

            let successCount = 0;
            let errorCount = 0;
            const errors = [];

            for (const file of files) {
                try {
                    const svgContent = await this.readFileAsText(file);
                    const fileName = file.name.replace('.svg', '');
                    const iconId = this.generateIconId(fileName);

                    // Check if ID already exists
                    if (this.iconsData.find(icon => icon.id === iconId)) {
                        errors.push(`Icon ID "${iconId}" already exists`);
                        errorCount++;
                        continue;
                    }

                    // Add the icon
                    const iconData = {
                        id: iconId,
                        name: this.generateIconName(fileName),
                        category: 'imported', // Default category for batch imports
                        svg: this.cleanSVG(svgContent)
                    };

                    this.iconsData.push(iconData);
                    this.categories.add('imported');
                    successCount++;

                } catch (error) {
                    errors.push(`${file.name}: ${error.message}`);
                    errorCount++;
                }
            }

            // Save and update UI
            if (successCount > 0) {
                this.saveIconsData();
                this.updateCategoriesDropdown();
                this.filterIcons();
                this.updateStats();
            }

            // Show results
            let message = `Import complete: ${successCount} icons added`;
            if (errorCount > 0) {
                message += `, ${errorCount} errors`;
                if (errors.length > 0) {
                    message += `\nErrors: ${errors.slice(0, 3).join(', ')}`;
                    if (errors.length > 3) {
                        message += ` and ${errors.length - 3} more...`;
                    }
                }
            }

            this.showUploadStatus(message, successCount > 0 ? 'success' : 'error');

        } catch (error) {
            console.error('Error processing multiple files:', error);
            this.showUploadStatus(`Error processing files: ${error.message}`, 'error');
        }
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    generateIconId(fileName) {
        // Convert filename to a valid icon ID
        return fileName
            .toLowerCase()
            .replace(/[^a-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }

    generateIconName(fileName) {
        // Convert filename to a readable name
        return fileName
            .replace(/[_-]/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    }

    showUploadStatus(message, type) {
        const statusEl = document.getElementById('fileUploadStatus');
        statusEl.innerHTML = `<div class="upload-status ${type}">${message}</div>`;
        statusEl.style.display = 'block';

        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                this.hideUploadStatus();
            }, 5000);
        }
    }

    hideUploadStatus() {
        const statusEl = document.getElementById('fileUploadStatus');
        statusEl.style.display = 'none';
    }

    setupNavigation() {
        const navButtons = document.querySelectorAll('.nav-btn');
        const sections = document.querySelectorAll('.admin-section');

        console.log('Navigation setup - Found nav buttons:', navButtons.length);
        console.log('Navigation setup - Found sections:', sections.length);

        navButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetSection = btn.dataset.section;
                console.log('Navigation clicked:', targetSection);

                // Update active nav button
                navButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // Show target section
                sections.forEach(section => {
                    section.classList.remove('active');
                });

                const targetSectionElement = document.getElementById(targetSection + 'Section');
                if (targetSectionElement) {
                    targetSectionElement.classList.add('active');
                    console.log('Activated section:', targetSection + 'Section');
                } else {
                    console.error('Section not found:', targetSection + 'Section');
                }

                // Load section-specific data
                if (targetSection === 'categories') {
                    this.renderCategories();
                } else if (targetSection === 'importExport') {
                    console.log('Import/Export section activated');
                }
            });
        });
    }

    setupCategoryManagement() {
        // Add category form
        document.getElementById('addCategoryForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addCategory();
        });
    }

    setupImportExport() {
        // Debug: Check if elements exist
        const elements = [
            'exportJsonBtn',
            'exportCsvBtn',
            'createBackupBtn',
            'importJsonBtn',
            'jsonFileInput'
        ];

        elements.forEach(id => {
            const element = document.getElementById(id);
            if (!element) {
                console.error(`Import/Export element not found: ${id}`);
            } else {
                console.log(`Import/Export element found: ${id}`);
            }
        });

        // Export buttons
        const exportJsonBtn = document.getElementById('exportJsonBtn');
        if (exportJsonBtn) {
            exportJsonBtn.addEventListener('click', () => {
                this.exportData('json');
            });
        }

        const exportCsvBtn = document.getElementById('exportCsvBtn');
        if (exportCsvBtn) {
            exportCsvBtn.addEventListener('click', () => {
                this.exportData('csv');
            });
        }

        const createBackupBtn = document.getElementById('createBackupBtn');
        if (createBackupBtn) {
            createBackupBtn.addEventListener('click', () => {
                this.createBackup();
            });
        }

        // Import functionality
        const importJsonBtn = document.getElementById('importJsonBtn');
        if (importJsonBtn) {
            importJsonBtn.addEventListener('click', () => {
                this.importData();
            });
        }
    }

    addCategory() {
        const categoryName = document.getElementById('newCategoryName').value.trim().toLowerCase();

        if (!categoryName) {
            this.showMessage('Please enter a category name', 'error');
            return;
        }

        if (this.categories.has(categoryName)) {
            this.showMessage('Category already exists', 'error');
            return;
        }

        this.categories.add(categoryName);
        this.updateCategoriesDropdown();
        this.renderCategories();
        this.updateStats();

        document.getElementById('newCategoryName').value = '';
        this.showMessage('Category added successfully!', 'success');
    }

    editCategory(oldName, newName) {
        if (!newName || newName === oldName) return;

        newName = newName.trim().toLowerCase();

        if (this.categories.has(newName)) {
            this.showMessage('Category name already exists', 'error');
            return;
        }

        // Update all icons with this category
        this.iconsData.forEach(icon => {
            if (icon.category === oldName) {
                icon.category = newName;
            }
        });

        // Update categories set
        this.categories.delete(oldName);
        this.categories.add(newName);

        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.renderCategories();
        this.filterIcons();
        this.updateStats();

        this.showMessage('Category updated successfully!', 'success');
    }

    deleteCategory(categoryName) {
        const iconsInCategory = this.iconsData.filter(icon => icon.category === categoryName);

        if (iconsInCategory.length > 0) {
            if (!confirm(`This category contains ${iconsInCategory.length} icons. Deleting it will move these icons to 'uncategorized'. Continue?`)) {
                return;
            }

            // Move icons to 'uncategorized'
            iconsInCategory.forEach(icon => {
                icon.category = 'uncategorized';
            });
            this.categories.add('uncategorized');
        }

        this.categories.delete(categoryName);
        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.renderCategories();
        this.filterIcons();
        this.updateStats();

        this.showMessage('Category deleted successfully!', 'success');
    }

    renderCategories() {
        const container = document.getElementById('categoriesList');
        container.innerHTML = '';

        if (this.categories.size === 0) {
            container.innerHTML = '<p style="text-align: center; color: #666;">No categories found. Add some categories to get started!</p>';
            return;
        }

        Array.from(this.categories).sort().forEach(category => {
            const iconCount = this.iconsData.filter(icon => icon.category === category).length;

            const categoryItem = document.createElement('div');
            categoryItem.className = 'category-item';
            categoryItem.innerHTML = `
                <div class="category-info">
                    <div class="category-name">${category}</div>
                    <div class="category-count">${iconCount} icons</div>
                </div>
                <div class="category-actions">
                    <button class="edit-category-btn" onclick="adminPanel.promptEditCategory('${category}')">Edit</button>
                    <button class="delete-category-btn" onclick="adminPanel.deleteCategory('${category}')">Delete</button>
                </div>
            `;
            container.appendChild(categoryItem);
        });
    }

    promptEditCategory(categoryName) {
        const newName = prompt(`Edit category name:`, categoryName);
        if (newName !== null) {
            this.editCategory(categoryName, newName);
        }
    }

    exportData(format) {
        try {
            let data, filename, mimeType;

            if (format === 'json') {
                data = JSON.stringify(this.iconsData, null, 2);
                filename = `icons-data-${this.getCurrentTimestamp()}.json`;
                mimeType = 'application/json';
            } else if (format === 'csv') {
                data = this.convertToCSV(this.iconsData);
                filename = `icons-data-${this.getCurrentTimestamp()}.csv`;
                mimeType = 'text/csv';
            }

            this.downloadFile(data, filename, mimeType);
            this.showMessage(`${format.toUpperCase()} export completed successfully!`, 'success');

        } catch (error) {
            console.error('Export error:', error);
            this.showMessage(`Error exporting ${format.toUpperCase()}: ${error.message}`, 'error');
        }
    }

    createBackup() {
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                version: '1.0',
                totalIcons: this.iconsData.length,
                categories: Array.from(this.categories),
                icons: this.iconsData
            };

            const data = JSON.stringify(backupData, null, 2);
            const filename = `icons-backup-${this.getCurrentTimestamp()}.json`;

            this.downloadFile(data, filename, 'application/json');
            this.showMessage('Backup created successfully!', 'success');

        } catch (error) {
            console.error('Backup error:', error);
            this.showMessage(`Error creating backup: ${error.message}`, 'error');
        }
    }

    async importData() {
        const fileInput = document.getElementById('jsonFileInput');
        const importMode = document.querySelector('input[name="importMode"]:checked').value;

        if (!fileInput.files.length) {
            this.showMessage('Please select a JSON file to import', 'error');
            return;
        }

        try {
            const file = fileInput.files[0];
            const jsonData = await this.readFileAsText(file);
            const importedData = JSON.parse(jsonData);

            // Validate imported data
            if (!Array.isArray(importedData) && !importedData.icons) {
                throw new Error('Invalid JSON format. Expected an array of icons or backup file.');
            }

            // Extract icons array (handle both direct array and backup format)
            const iconsToImport = Array.isArray(importedData) ? importedData : importedData.icons;

            if (!Array.isArray(iconsToImport)) {
                throw new Error('No valid icons array found in the imported file.');
            }

            // Validate icon structure
            const validIcons = iconsToImport.filter(icon =>
                icon.id && icon.name && icon.category && icon.svg
            );

            if (validIcons.length === 0) {
                throw new Error('No valid icons found in the imported file.');
            }

            let importedCount = 0;
            let skippedCount = 0;

            if (importMode === 'replace') {
                // Replace all data
                this.iconsData = validIcons;
                importedCount = validIcons.length;
                console.log(`Import Replace: Replaced all data with ${importedCount} icons`);
            } else {
                // Merge with existing data
                validIcons.forEach(icon => {
                    const existingIndex = this.iconsData.findIndex(existing => existing.id === icon.id);
                    if (existingIndex >= 0) {
                        // Update existing icon
                        this.iconsData[existingIndex] = icon;
                        skippedCount++;
                    } else {
                        // Add new icon
                        this.iconsData.push(icon);
                        importedCount++;
                    }
                });
                console.log(`Import Merge: Added ${importedCount} new icons, updated ${skippedCount} existing icons`);
            }

            // Update categories
            this.categories.clear();
            this.iconsData.forEach(icon => {
                this.categories.add(icon.category);
            });

            // Save and update UI - this is the critical step that saves to server
            console.log('Saving imported data to server...');
            await this.saveIconsData();

            this.updateCategoriesDropdown();
            this.filterIcons();
            this.updateStats();
            this.updateLastModified();

            // Show final success message
            if (importMode === 'replace') {
                this.showMessage(`✅ Data replaced successfully! Imported ${importedCount} icons and saved to server.`, 'success');
            } else {
                this.showMessage(`✅ Data merged successfully! Added ${importedCount} new icons, updated ${skippedCount} existing icons, and saved to server.`, 'success');
            }

            // Clear file input
            fileInput.value = '';

        } catch (error) {
            console.error('Import error:', error);
            this.showMessage(`Error importing data: ${error.message}`, 'error');
        }
    }

    convertToCSV(data) {
        if (!data.length) return '';

        const headers = ['ID', 'Name', 'Category', 'SVG Code'];
        const csvRows = [headers.join(',')];

        data.forEach(icon => {
            const row = [
                `"${icon.id}"`,
                `"${icon.name}"`,
                `"${icon.category}"`,
                `"${icon.svg.replace(/"/g, '""')}"` // Escape quotes in SVG
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    }

    downloadFile(data, filename, mimeType) {
        const blob = new Blob([data], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    getCurrentTimestamp() {
        const now = new Date();
        return now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '-');
    }

    testButtons() {
        // Test all edit and delete buttons
        const editButtons = document.querySelectorAll('.edit-btn');
        const deleteButtons = document.querySelectorAll('.delete-btn');

        console.log(`Found ${editButtons.length} edit buttons and ${deleteButtons.length} delete buttons`);

        let message = `Button Test Results:\n`;
        message += `✅ Edit buttons: ${editButtons.length} found\n`;
        message += `✅ Delete buttons: ${deleteButtons.length} found\n`;

        // Test visibility
        let visibleEdit = 0;
        let visibleDelete = 0;

        editButtons.forEach(btn => {
            const style = window.getComputedStyle(btn);
            if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
                visibleEdit++;
            }
        });

        deleteButtons.forEach(btn => {
            const style = window.getComputedStyle(btn);
            if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
                visibleDelete++;
            }
        });

        message += `✅ Visible edit buttons: ${visibleEdit}\n`;
        message += `✅ Visible delete buttons: ${visibleDelete}\n`;

        if (visibleEdit === editButtons.length && visibleDelete === deleteButtons.length) {
            message += `\n🎉 All buttons are visible and working!`;
        } else {
            message += `\n⚠️ Some buttons may have visibility issues.`;
        }

        alert(message);
        this.showMessage('Button test completed - check console for details', 'info');
    }

    editIcon(iconId) {
        const icon = this.iconsData.find(i => i.id === iconId);
        if (!icon) {
            this.showMessage('Icon not found!', 'error');
            return;
        }

        // Fill form with icon data
        document.getElementById('iconId').value = icon.id;
        document.getElementById('iconName').value = icon.name;
        document.getElementById('iconCategory').value = icon.category;
        document.getElementById('iconSvg').value = icon.svg;

        // Update form for editing mode
        this.editingIconId = iconId;
        document.querySelector('#addIconForm button[type="submit"]').textContent = 'Update Icon';

        // Show preview of the existing icon
        this.previewSVG();

        // Scroll to form
        document.getElementById('addIconForm').scrollIntoView({ behavior: 'smooth' });
    }

    deleteIcon(iconId) {
        if (!confirm('Are you sure you want to delete this icon?')) {
            return;
        }

        this.iconsData = this.iconsData.filter(icon => icon.id !== iconId);

        // Update categories set
        this.categories.clear();
        this.iconsData.forEach(icon => {
            this.categories.add(icon.category);
        });

        this.saveIconsData();
        this.updateCategoriesDropdown();
        this.filterIcons();
        this.updateStats();
        this.showMessage('Icon deleted successfully!', 'success');
    }

    renderIcons() {
        const container = document.getElementById('iconsGrid');
        const iconsCount = document.getElementById('iconsCount');

        container.innerHTML = '';

        if (this.filteredIcons.length === 0) {
            if (this.iconsData.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; grid-column: 1 / -1;">No icons found. Add some icons to get started!</p>';
                iconsCount.textContent = 'No icons found.';
            } else {
                container.innerHTML = '<p style="text-align: center; color: #666; grid-column: 1 / -1;">No icons match your search criteria.</p>';
                iconsCount.textContent = `No icons match your search criteria. (${this.iconsData.length} total icons)`;
            }
            return;
        }

        iconsCount.textContent = `Showing ${this.filteredIcons.length} of ${this.iconsData.length} icons`;

        this.filteredIcons.forEach(icon => {
            const iconCard = document.createElement('div');
            iconCard.className = 'icon-card';
            iconCard.innerHTML = `
                <div class="icon-preview">
                    ${icon.svg}
                </div>
                <div class="icon-info">
                    <h3>${icon.name}</h3>
                    <p>ID: ${icon.id}</p>
                    <p>Category: ${icon.category}</p>
                </div>
                <div class="icon-actions">
                    <button class="edit-btn" onclick="adminPanel.editIcon('${icon.id}')">Edit</button>
                    <button class="delete-btn" onclick="adminPanel.deleteIcon('${icon.id}')">Delete</button>
                </div>
            `;
            container.appendChild(iconCard);
        });
    }

    updateStats() {
        document.getElementById('totalIcons').textContent = this.iconsData.length;
        document.getElementById('totalCategories').textContent = this.categories.size;

        // Update top categories
        const categoryCounts = {};
        this.iconsData.forEach(icon => {
            categoryCounts[icon.category] = (categoryCounts[icon.category] || 0) + 1;
        });

        const topCategories = Object.entries(categoryCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([category, count]) => `${category} (${count})`)
            .join(', ');

        document.getElementById('topCategories').textContent = topCategories || 'None';
    }

    updateLastModified() {
        const now = new Date();
        const timestamp = now.toLocaleDateString() + ', ' + now.toLocaleTimeString();
        document.getElementById('lastUpdated').textContent = timestamp;
    }

    showMessage(text, type) {
        const messageEl = document.getElementById('message');
        messageEl.textContent = text;
        messageEl.className = `message ${type}`;
        messageEl.style.display = 'block';

        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 5000);
    }
}

// Initialize admin panel
const adminPanel = new AdminPanel();

// For testing: If no data exists, load sample data
window.loadSampleData = function() {
    const sampleData = [
        {
            "id": "arrow-up",
            "name": "Arrow Up",
            "category": "navigation",
            "svg": "<svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m18 15-6-6-6 6\"/></svg>"
        },
        {
            "id": "arrow-down",
            "name": "Arrow Down",
            "category": "navigation",
            "svg": "<svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m6 9 6 6 6-6\"/></svg>"
        },
        {
            "id": "home",
            "name": "Home",
            "category": "interface",
            "svg": "<svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/><polyline points=\"9,22 9,12 15,12 15,22\"/></svg>"
        }
    ];

    localStorage.setItem('iconsData', JSON.stringify(sampleData));
    adminPanel.loadIconsData();
    console.log('Sample data loaded!');
};
