<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .test-data { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Admin Panel Import/Export Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test Overview</h2>
        <p>This page tests all the Import/Export functionality that should be available in the admin panel:</p>
        <ul>
            <li>Export JSON - Download icon data as JSON file</li>
            <li>Export CSV - Download icon data as CSV file</li>
            <li>Create Backup - Download complete backup with metadata</li>
            <li>Import JSON - Upload and import icon data</li>
            <li>Replace vs Merge options - Different import modes</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Sample Test Data</h2>
        <div class="test-data" id="sampleData"></div>
        <button onclick="generateTestData()">Generate Test Data</button>
    </div>

    <div class="test-section">
        <h2>Export Tests</h2>
        <button onclick="testExportJSON()">Test Export JSON</button>
        <button onclick="testExportCSV()">Test Export CSV</button>
        <button onclick="testCreateBackup()">Test Create Backup</button>
        <div id="exportResults"></div>
    </div>

    <div class="test-section">
        <h2>Import Tests</h2>
        <p>First, export some test data above, then use the files to test import functionality:</p>
        <input type="file" id="importFileInput" accept=".json" style="margin: 10px 0;">
        <br>
        <label style="display: block; margin: 5px 0;">
            <input type="radio" name="importMode" value="replace" checked> Replace existing data
        </label>
        <label style="display: block; margin: 5px 0;">
            <input type="radio" name="importMode" value="merge"> Merge with existing data
        </label>
        <br>
        <button onclick="testImportJSON()">Test Import JSON</button>
        <div id="importResults"></div>
    </div>

    <div class="test-section">
        <h2>Admin Panel Integration Test</h2>
        <p>Click the button below to open the actual admin panel and verify the Import/Export tab works:</p>
        <button onclick="openAdminPanel()">Open Admin Panel</button>
        <div class="test-result info">
            <strong>Manual Test Steps:</strong>
            <ol>
                <li>Click "Open Admin Panel" above</li>
                <li>Login with password: @admin1234</li>
                <li>Click on the "Import/Export" tab</li>
                <li>Verify all buttons are visible: Export JSON, Export CSV, Create Backup</li>
                <li>Verify import section shows: file input, radio buttons, Import JSON button</li>
                <li>Test each export button to download files</li>
                <li>Test import functionality with downloaded files</li>
            </ol>
        </div>
    </div>

    <script>
        let testData = [];

        function generateTestData() {
            testData = [
                {
                    "id": "test-home",
                    "name": "Test Home",
                    "category": "interface",
                    "svg": "<svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/><polyline points=\"9,22 9,12 15,12 15,22\"/></svg>"
                },
                {
                    "id": "test-user",
                    "name": "Test User",
                    "category": "interface",
                    "svg": "<svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"/><circle cx=\"12\" cy=\"7\" r=\"4\"/></svg>"
                },
                {
                    "id": "test-settings",
                    "name": "Test Settings",
                    "category": "interface",
                    "svg": "<svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"12\" cy=\"12\" r=\"3\"/><path d=\"M12 1v6m0 6v6m11-7h-6m-6 0H1\"/></svg>"
                }
            ];

            document.getElementById('sampleData').textContent = JSON.stringify(testData, null, 2);
            showResult('exportResults', 'Test data generated successfully!', 'success');
        }

        function testExportJSON() {
            if (!testData.length) {
                showResult('exportResults', 'Please generate test data first!', 'error');
                return;
            }

            try {
                const data = JSON.stringify(testData, null, 2);
                const filename = `test-export-${getCurrentTimestamp()}.json`;
                downloadFile(data, filename, 'application/json');
                showResult('exportResults', `✓ JSON export successful! File: ${filename}`, 'success');
            } catch (error) {
                showResult('exportResults', `✗ JSON export failed: ${error.message}`, 'error');
            }
        }

        function testExportCSV() {
            if (!testData.length) {
                showResult('exportResults', 'Please generate test data first!', 'error');
                return;
            }

            try {
                const data = convertToCSV(testData);
                const filename = `test-export-${getCurrentTimestamp()}.csv`;
                downloadFile(data, filename, 'text/csv');
                showResult('exportResults', `✓ CSV export successful! File: ${filename}`, 'success');
            } catch (error) {
                showResult('exportResults', `✗ CSV export failed: ${error.message}`, 'error');
            }
        }

        function testCreateBackup() {
            if (!testData.length) {
                showResult('exportResults', 'Please generate test data first!', 'error');
                return;
            }

            try {
                const backupData = {
                    timestamp: new Date().toISOString(),
                    version: '1.0',
                    totalIcons: testData.length,
                    categories: ['interface'],
                    icons: testData
                };
                const data = JSON.stringify(backupData, null, 2);
                const filename = `test-backup-${getCurrentTimestamp()}.json`;
                downloadFile(data, filename, 'application/json');
                showResult('exportResults', `✓ Backup creation successful! File: ${filename}`, 'success');
            } catch (error) {
                showResult('exportResults', `✗ Backup creation failed: ${error.message}`, 'error');
            }
        }

        async function testImportJSON() {
            const fileInput = document.getElementById('importFileInput');
            const importMode = document.querySelector('input[name="importMode"]:checked').value;

            if (!fileInput.files.length) {
                showResult('importResults', 'Please select a JSON file to import!', 'error');
                return;
            }

            try {
                const file = fileInput.files[0];
                const jsonData = await readFileAsText(file);
                const importedData = JSON.parse(jsonData);

                // Validate imported data
                if (!Array.isArray(importedData) && !importedData.icons) {
                    throw new Error('Invalid JSON format. Expected an array of icons or backup file.');
                }

                const iconsToImport = Array.isArray(importedData) ? importedData : importedData.icons;
                const validIcons = iconsToImport.filter(icon =>
                    icon.id && icon.name && icon.category && icon.svg
                );

                if (validIcons.length === 0) {
                    throw new Error('No valid icons found in the imported file.');
                }

                showResult('importResults', 
                    `✓ Import test successful!<br>
                     Mode: ${importMode}<br>
                     File: ${file.name}<br>
                     Valid icons: ${validIcons.length}<br>
                     Sample icon: ${validIcons[0].name} (${validIcons[0].id})`, 
                    'success');
            } catch (error) {
                showResult('importResults', `✗ Import test failed: ${error.message}`, 'error');
            }
        }

        function openAdminPanel() {
            window.open('admin-new.html', '_blank');
        }

        // Helper functions
        function convertToCSV(data) {
            if (!data.length) return '';
            const headers = ['ID', 'Name', 'Category', 'SVG Code'];
            const csvRows = [headers.join(',')];
            data.forEach(icon => {
                const row = [
                    `"${icon.id}"`,
                    `"${icon.name}"`,
                    `"${icon.category}"`,
                    `"${icon.svg.replace(/"/g, '""')}"`
                ];
                csvRows.push(row.join(','));
            });
            return csvRows.join('\n');
        }

        function downloadFile(data, filename, mimeType) {
            const blob = new Blob([data], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        function getCurrentTimestamp() {
            const now = new Date();
            return now.toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '-');
        }

        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = () => reject(new Error('Failed to read file'));
                reader.readAsText(file);
            });
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            element.appendChild(resultDiv);
        }

        // Initialize with test data
        window.onload = function() {
            generateTestData();
        };
    </script>
</body>
</html>
