// SVG Icons Data will be loaded from JSON file
let iconsData = [];

// Function to load icons data from JSON file
async function loadIconsData() {
    try {
        const response = await fetch('icons-data.json');
        if (!response.ok) {
            throw new Error('Failed to load icons data');
        }
        iconsData = await response.json();
        return iconsData;
    } catch (error) {
        console.error('Error loading icons data:', error);
        return [];
    }
}

// SVG Icon Library Application
class IconLibrary {
    constructor() {
        this.currentColor = '#333333';
        this.currentSize = 48; // Increased default size for better visibility
        this.filteredIcons = [];
        this.selectedIcon = null;
        this.lastLoadTime = null;
        this.refreshInterval = null;
        this.init();
    }

    async init() {
        try {
            // Load icons data first
            await loadIconsData();
            this.filteredIcons = [...iconsData];
            this.lastLoadTime = Date.now();

            this.setupEventListeners();
            this.renderIcons();
            this.updateSizeDisplay();
            this.showLoadingComplete();

            // Set up periodic refresh check (every 30 seconds)
            this.refreshInterval = setInterval(() => {
                this.checkForUpdates();
            }, 30000);

        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Failed to load icon library. Please refresh the page.');
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('search-input');
        searchInput.addEventListener('input', (e) => {
            this.filterIcons(e.target.value, document.getElementById('category-filter').value);
        });
        
        // Category filter
        const categoryFilter = document.getElementById('category-filter');
        categoryFilter.addEventListener('change', (e) => {
            this.filterIcons(document.getElementById('search-input').value, e.target.value);
        });
        
        // Color picker
        const colorPicker = document.getElementById('color-picker');
        colorPicker.addEventListener('input', (e) => {
            this.updateColor(e.target.value);
        });
        
        // Color presets
        const colorPresets = document.querySelectorAll('.color-preset');
        colorPresets.forEach(preset => {
            preset.addEventListener('click', (e) => {
                const color = e.target.dataset.color;
                this.updateColor(color);
                document.getElementById('color-picker').value = color;
                
                // Update active preset
                colorPresets.forEach(p => p.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
        
        // Size slider
        const sizeSlider = document.getElementById('size-slider');
        sizeSlider.addEventListener('input', (e) => {
            this.updateSize(parseInt(e.target.value));
        });

        // Add refresh button functionality
        this.addRefreshButton();
    }

    addRefreshButton() {
        const header = document.querySelector('.header');
        if (header) {
            const refreshBtn = document.createElement('button');
            refreshBtn.innerHTML = '🔄 Refresh Icons';
            refreshBtn.className = 'refresh-btn';
            refreshBtn.style.cssText = `
                background: rgba(255,255,255,0.2);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
                cursor: pointer;
                margin-left: 15px;
                transition: all 0.3s ease;
            `;
            refreshBtn.addEventListener('click', () => this.refreshData());
            refreshBtn.addEventListener('mouseover', () => {
                refreshBtn.style.background = 'rgba(255,255,255,0.3)';
            });
            refreshBtn.addEventListener('mouseout', () => {
                refreshBtn.style.background = 'rgba(255,255,255,0.2)';
            });

            const adminLink = header.querySelector('a');
            if (adminLink) {
                adminLink.parentNode.appendChild(refreshBtn);
            }
        }
    }

    async checkForUpdates() {
        try {
            const response = await fetch('icons-data.json', {
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            if (response.ok) {
                const lastModified = response.headers.get('Last-Modified');
                if (lastModified) {
                    const modifiedTime = new Date(lastModified).getTime();
                    if (modifiedTime > this.lastLoadTime) {
                        this.showUpdateAvailable();
                    }
                }
            }
        } catch (error) {
            console.warn('Failed to check for updates:', error);
        }
    }

    showUpdateAvailable() {
        const notification = document.createElement('div');
        notification.className = 'update-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 1000;
            font-size: 14px;
            max-width: 300px;
        `;
        notification.innerHTML = `
            <div style="margin-bottom: 10px;">📦 New icons available!</div>
            <button onclick="iconLibrary.refreshData()" style="background: white; color: #28a745; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-right: 10px;">Refresh</button>
            <button onclick="this.parentNode.remove()" style="background: transparent; color: white; border: 1px solid white; padding: 5px 10px; border-radius: 4px; cursor: pointer;">Dismiss</button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }

    async refreshData() {
        try {
            this.showLoadingIndicator();
            await loadIconsData();
            this.filteredIcons = [...iconsData];
            this.lastLoadTime = Date.now();

            // Reapply current filters
            const searchTerm = document.getElementById('search-input').value;
            const category = document.getElementById('category-filter').value;
            this.filterIcons(searchTerm, category);

            this.hideLoadingIndicator();
            this.showNotification('Icons refreshed successfully!');

            // Remove any update notifications
            const updateNotifications = document.querySelectorAll('.update-notification');
            updateNotifications.forEach(n => n.remove());

        } catch (error) {
            this.hideLoadingIndicator();
            this.showNotification('Failed to refresh icons', 'error');
            console.error('Refresh failed:', error);
        }
    }

    showLoadingIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'loading-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 2000;
            text-align: center;
        `;
        indicator.innerHTML = `
            <div style="margin-bottom: 10px;">🔄</div>
            <div>Loading icons...</div>
        `;
        document.body.appendChild(indicator);
    }

    hideLoadingIndicator() {
        const indicator = document.getElementById('loading-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    showLoadingComplete() {
        const count = iconsData.length;
        const categories = new Set(iconsData.map(icon => icon.category)).size;
        console.log(`✅ Loaded ${count} icons across ${categories} categories`);

        // Update subtitle and stats
        const subtitle = document.getElementById('subtitle');
        const statsDisplay = document.getElementById('stats-display');

        if (subtitle) {
            subtitle.textContent = `${count} minimalistic icons for your projects`;
        }

        if (statsDisplay) {
            statsDisplay.textContent = `${categories} categories • Last updated: ${new Date().toLocaleTimeString()}`;
        }
    }

    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #dc3545;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 1000;
            text-align: center;
        `;
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);

        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }
    
    filterIcons(searchTerm, category) {
        this.filteredIcons = iconsData.filter(icon => {
            const matchesSearch = !searchTerm || 
                icon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                icon.category.toLowerCase().includes(searchTerm.toLowerCase());
            
            const matchesCategory = category === 'all' || icon.category === category;
            
            return matchesSearch && matchesCategory;
        });
        
        this.renderIcons();
    }
    
    renderIcons() {
        const iconsGrid = document.getElementById('icons-grid');
        if (!iconsGrid) {
            console.error('Icons grid container not found!');
            return;
        }

        iconsGrid.innerHTML = '';

        this.filteredIcons.forEach(icon => {
            const iconElement = this.createIconElement(icon);
            iconsGrid.appendChild(iconElement);
        });
    }
    
    createIconElement(icon) {
        const iconItem = document.createElement('div');
        iconItem.className = 'icon-item';
        iconItem.dataset.iconId = icon.id;
        
        iconItem.innerHTML = `
            <div class="icon-display" style="color: ${this.currentColor}; width: ${this.currentSize}px; height: ${this.currentSize}px;">
                ${icon.svg}
            </div>
            <div class="icon-name">${icon.name}</div>
            <div class="icon-category">${icon.category}</div>
            <div class="icon-actions">
                <button class="action-btn copy-btn" onclick="iconLibrary.copyIcon('${icon.id}')">Copy</button>
                <button class="action-btn export-btn" onclick="iconLibrary.exportIcon('${icon.id}')">Download</button>
            </div>
        `;
        
        // Add click event for preview
        iconItem.addEventListener('click', () => {
            this.selectIcon(icon);
        });
        
        return iconItem;
    }
    
    selectIcon(icon) {
        // Remove previous selection
        document.querySelectorAll('.icon-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // Add selection to current icon
        const iconElement = document.querySelector(`[data-icon-id="${icon.id}"]`);
        if (iconElement) {
            iconElement.classList.add('selected');
        }
        
        this.selectedIcon = icon;
        this.showPreview(icon);
    }
    
    showPreview(icon) {
        const previewArea = document.getElementById('preview-area');
        const previewIcon = document.getElementById('preview-icon');
        const previewName = document.getElementById('preview-name');
        const previewCategory = document.getElementById('preview-category');
        
        // Show preview area
        previewArea.style.display = 'block';
        
        // Update preview content
        previewIcon.innerHTML = icon.svg;
        previewIcon.style.color = this.currentColor;
        previewName.textContent = icon.name;
        previewCategory.textContent = icon.category;
        
        // Update size previews
        const sizes = [16, 24, 32, 48, 64];
        sizes.forEach(size => {
            const previewElement = document.getElementById(`preview-${size}`);
            previewElement.innerHTML = icon.svg;
            previewElement.style.color = this.currentColor;
        });
    }
    
    updateColor(color) {
        this.currentColor = color;
        
        // Update all displayed icons
        document.querySelectorAll('.icon-display').forEach(display => {
            display.style.color = color;
        });
        
        // Update preview if visible
        if (this.selectedIcon) {
            this.showPreview(this.selectedIcon);
        }
    }
    
    updateSize(size) {
        this.currentSize = size;
        this.updateSizeDisplay();
        
        // Update all displayed icons
        document.querySelectorAll('.icon-display').forEach(display => {
            display.style.width = `${size}px`;
            display.style.height = `${size}px`;
        });
    }
    
    updateSizeDisplay() {
        document.getElementById('size-value').textContent = `${this.currentSize}px`;
    }
    
    copyIcon(iconId) {
        const icon = iconsData.find(i => i.id === iconId);
        if (!icon) return;
        
        const svgWithColor = icon.svg.replace('stroke="currentColor"', `stroke="${this.currentColor}"`);
        
        navigator.clipboard.writeText(svgWithColor).then(() => {
            this.showNotification('SVG copied to clipboard!');
        }).catch(err => {
            console.error('Failed to copy: ', err);
            this.showNotification('Failed to copy SVG', 'error');
        });
    }
    
    exportIcon(iconId) {
        const icon = iconsData.find(i => i.id === iconId);
        if (!icon) return;
        
        const svgWithColor = icon.svg.replace('stroke="currentColor"', `stroke="${this.currentColor}"`);
        const svgContent = `<?xml version="1.0" encoding="UTF-8"?>\n${svgWithColor}`;
        
        const blob = new Blob([svgContent], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${icon.id}.svg`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification(`${icon.name} exported successfully!`);
    }
    
    showNotification(message, type = 'success') {
        const notification = document.getElementById('notification');
        const notificationText = document.getElementById('notification-text');
        
        notificationText.textContent = message;
        notification.className = `notification ${type}`;
        notification.classList.add('show');
        
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    window.iconLibrary = new IconLibrary();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.iconLibrary && window.iconLibrary.refreshInterval) {
        clearInterval(window.iconLibrary.refreshInterval);
    }
});
