<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Get the JSON data from the request body
    $input = file_get_contents('php://input');
    $iconsData = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }
    
    if (!is_array($iconsData)) {
        throw new Exception('Data must be an array');
    }
    
    // Validate the data structure
    foreach ($iconsData as $icon) {
        if (!isset($icon['id']) || !isset($icon['name']) || !isset($icon['category']) || !isset($icon['svg'])) {
            throw new Exception('Invalid icon data structure');
        }
    }
    
    // Save to icons-data.json file
    $jsonString = json_encode($iconsData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
    if (file_put_contents('icons-data.json', $jsonString) === false) {
        throw new Exception('Failed to write to file');
    }
    
    // Create a backup with timestamp
    $backupFilename = 'backups/icons-data-' . date('Y-m-d-H-i-s') . '.json';
    
    // Create backups directory if it doesn't exist
    if (!is_dir('backups')) {
        mkdir('backups', 0755, true);
    }
    
    file_put_contents($backupFilename, $jsonString);
    
    echo json_encode([
        'success' => true,
        'message' => 'Icons data saved successfully',
        'timestamp' => date('c'),
        'totalIcons' => count($iconsData),
        'backupFile' => $backupFilename
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'timestamp' => date('c')
    ]);
}
?>
